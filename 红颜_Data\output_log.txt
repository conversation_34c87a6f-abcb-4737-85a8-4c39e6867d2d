Initialize engine version: 5.2.4f1 (98095704e6fe)
GfxDevice: creating device client; threaded=1
Direct3D:
    Version:  Direct3D 11.0 [level 11.0]
    Renderer: Intel(R) Iris(R) Xe Graphics (ID=0x46a6)
    Vendor:   Intel
    VRAM:     128 MB
Begin MonoManager ReloadAssembly
Platform assembly: D:\QuarkNetdiskDownload\HYYM\HYYM\红颜_Data\Managed\UnityEngine.dll (this message is harmless)
Loading D:\QuarkNetdiskDownload\HYYM\HYYM\红颜_Data\Managed\UnityEngine.dll into Unity Child Domain
Platform assembly: D:\QuarkNetdiskDownload\HYYM\HYYM\红颜_Data\Managed\Assembly-CSharp-firstpass.dll (this message is harmless)
Loading D:\QuarkNetdiskDownload\HYYM\HYYM\红颜_Data\Managed\Assembly-CSharp-firstpass.dll into Unity Child Domain
Platform assembly: D:\QuarkNetdiskDownload\HYYM\HYYM\红颜_Data\Managed\Assembly-CSharp.dll (this message is harmless)
Loading D:\QuarkNetdiskDownload\HYYM\HYYM\红颜_Data\Managed\Assembly-CSharp.dll into Unity Child Domain
Platform assembly: D:\QuarkNetdiskDownload\HYYM\HYYM\红颜_Data\Managed\UnityEngine.UI.dll (this message is harmless)
Loading D:\QuarkNetdiskDownload\HYYM\HYYM\红颜_Data\Managed\UnityEngine.UI.dll into Unity Child Domain
Platform assembly: D:\QuarkNetdiskDownload\HYYM\HYYM\红颜_Data\Managed\UnityEngine.Networking.dll (this message is harmless)
Loading D:\QuarkNetdiskDownload\HYYM\HYYM\红颜_Data\Managed\UnityEngine.Networking.dll into Unity Child Domain
Platform assembly: D:\QuarkNetdiskDownload\HYYM\HYYM\红颜_Data\Managed\DOTween.dll (this message is harmless)
Loading D:\QuarkNetdiskDownload\HYYM\HYYM\红颜_Data\Managed\DOTween.dll into Unity Child Domain
Platform assembly: D:\QuarkNetdiskDownload\HYYM\HYYM\红颜_Data\Managed\DOTween43.dll (this message is harmless)
Loading D:\QuarkNetdiskDownload\HYYM\HYYM\红颜_Data\Managed\DOTween43.dll into Unity Child Domain
Platform assembly: D:\QuarkNetdiskDownload\HYYM\HYYM\红颜_Data\Managed\DOTween46.dll (this message is harmless)
Loading D:\QuarkNetdiskDownload\HYYM\HYYM\红颜_Data\Managed\DOTween46.dll into Unity Child Domain
Platform assembly: D:\QuarkNetdiskDownload\HYYM\HYYM\红颜_Data\Managed\Debuger.dll (this message is harmless)
Loading D:\QuarkNetdiskDownload\HYYM\HYYM\红颜_Data\Managed\Debuger.dll into Unity Child Domain
Platform assembly: D:\QuarkNetdiskDownload\HYYM\HYYM\红颜_Data\Managed\System.Core.dll (this message is harmless)
- Completed reload, in  0.720 seconds
<RI> Initializing input.

<RI> Input initialized.

desktop: 1920x1080 60Hz; virtual: 1920x1080 at 0,0
<RI> Initialized touch support.

Platform assembly: D:\QuarkNetdiskDownload\HYYM\HYYM\红颜_Data\Managed\System.dll (this message is harmless)
Platform assembly: D:\QuarkNetdiskDownload\HYYM\HYYM\红颜_Data\Managed\System.Xml.dll (this message is harmless)
Platform assembly: D:\QuarkNetdiskDownload\HYYM\HYYM\红颜_Data\Managed\System.Configuration.dll (this message is harmless)
Unloading 4 Unused Serialized files (Serialized files now loaded: 0)
Setting up 4 worker threads for Enlighten.
  Thread -> id: 57f0 -> priority: 1 
  Thread -> id: 3b28 -> priority: 1 
  Thread -> id: 56c4 -> priority: 1 
  Thread -> id: 1304 -> priority: 1 
UnloadTime: 2.179200 ms

Unloading 25 unused Assets to reduce memory usage. Loaded Objects now: 506.
Total: 0.527400 ms (FindLiveObjects: 0.045900 ms CreateObjectMapping: 0.025400 ms MarkObjects: 0.443800 ms  DeleteObjects: 0.011400 ms)

Unloading 1 Unused Serialized files (Serialized files now loaded: 0)

Unloading 32 unused Assets to reduce memory usage. Loaded Objects now: 533.
Total: 0.564100 ms (FindLiveObjects: 0.024200 ms CreateObjectMapping: 0.014900 ms MarkObjects: 0.504600 ms  DeleteObjects: 0.019800 ms)




System.TypeInitializationException: An exception was thrown by the type initializer for Mono.CSharp.CSharpCodeCompiler ---> System.IO.FileNotFoundException: 

  at Mono.CSharp.CSharpCodeCompiler..cctor () [0x00000] in <filename unknown>:0 

  --- End of inner exception stack trace ---

  at Microsoft.CSharp.CSharpCodeProvider.CreateCompiler () [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.GenerateSerializers (System.Xml.Serialization.GenerationBatch batch, System.CodeDom.Compiler.CompilerParameters cp) [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.RunSerializerGeneration (System.Object obj) [0x00000] in <filename unknown>:0 

System.TypeInitializationException: An exception was thrown by the type initializer for Mono.CSharp.CSharpCodeCompiler ---> System.IO.FileNotFoundException: 

  at Mono.CSharp.CSharpCodeCompiler..cctor () [0x00000] in <filename unknown>:0 

  --- End of inner exception stack trace ---

  at Microsoft.CSharp.CSharpCodeProvider.CreateCompiler () [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.GenerateSerializers (System.Xml.Serialization.GenerationBatch batch, System.CodeDom.Compiler.CompilerParameters cp) [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.RunSerializerGeneration (System.Object obj) [0x00000] in <filename unknown>:0 

System.TypeInitializationException: An exception was thrown by the type initializer for Mono.CSharp.CSharpCodeCompiler ---> System.IO.FileNotFoundException: 

  at Mono.CSharp.CSharpCodeCompiler..cctor () [0x00000] in <filename unknown>:0 

  --- End of inner exception stack trace ---

  at Microsoft.CSharp.CSharpCodeProvider.CreateCompiler () [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.GenerateSerializers (System.Xml.Serialization.GenerationBatch batch, System.CodeDom.Compiler.CompilerParameters cp) [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.RunSerializerGeneration (System.Object obj) [0x00000] in <filename unknown>:0 

System.TypeInitializationException: An exception was thrown by the type initializer for Mono.CSharp.CSharpCodeCompiler ---> System.IO.FileNotFoundException: 

  at Mono.CSharp.CSharpCodeCompiler..cctor () [0x00000] in <filename unknown>:0 

  --- End of inner exception stack trace ---

  at Microsoft.CSharp.CSharpCodeProvider.CreateCompiler () [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.GenerateSerializers (System.Xml.Serialization.GenerationBatch batch, System.CodeDom.Compiler.CompilerParameters cp) [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.RunSerializerGeneration (System.Object obj) [0x00000] in <filename unknown>:0 

System.TypeInitializationException: An exception was thrown by the type initializer for Mono.CSharp.CSharpCodeCompiler ---> System.IO.FileNotFoundException: 

  at Mono.CSharp.CSharpCodeCompiler..cctor () [0x00000] in <filename unknown>:0 

  --- End of inner exception stack trace ---

  at Microsoft.CSharp.CSharpCodeProvider.CreateCompiler () [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.GenerateSerializers (System.Xml.Serialization.GenerationBatch batch, System.CodeDom.Compiler.CompilerParameters cp) [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.RunSerializerGeneration (System.Object obj) [0x00000] in <filename unknown>:0 

System.TypeInitializationException: An exception was thrown by the type initializer for Mono.CSharp.CSharpCodeCompiler ---> System.IO.FileNotFoundException: 

  at Mono.CSharp.CSharpCodeCompiler..cctor () [0x00000] in <filename unknown>:0 

  --- End of inner exception stack trace ---

  at Microsoft.CSharp.CSharpCodeProvider.CreateCompiler () [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.GenerateSerializers (System.Xml.Serialization.GenerationBatch batch, System.CodeDom.Compiler.CompilerParameters cp) [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.RunSerializerGeneration (System.Object obj) [0x00000] in <filename unknown>:0 

System.TypeInitializationException: An exception was thrown by the type initializer for Mono.CSharp.CSharpCodeCompiler ---> System.IO.FileNotFoundException: 

  at Mono.CSharp.CSharpCodeCompiler..cctor () [0x00000] in <filename unknown>:0 

  --- End of inner exception stack trace ---

  at Microsoft.CSharp.CSharpCodeProvider.CreateCompiler () [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.GenerateSerializers (System.Xml.Serialization.GenerationBatch batch, System.CodeDom.Compiler.CompilerParameters cp) [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.RunSerializerGeneration (System.Object obj) [0x00000] in <filename unknown>:0 

System.TypeInitializationException: An exception was thrown by the type initializer for Mono.CSharp.CSharpCodeCompiler ---> System.IO.FileNotFoundException: 

  at Mono.CSharp.CSharpCodeCompiler..cctor () [0x00000] in <filename unknown>:0 

  --- End of inner exception stack trace ---

  at Microsoft.CSharp.CSharpCodeProvider.CreateCompiler () [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.GenerateSerializers (System.Xml.Serialization.GenerationBatch batch, System.CodeDom.Compiler.CompilerParameters cp) [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.RunSerializerGeneration (System.Object obj) [0x00000] in <filename unknown>:0 

重复key:曼陀支线姬雪2,xml=storysPY.xml
 
(Filename: C:/buildslave/unity/build/artifacts/generated/common/runtime/UnityEngineDebugBindings.gen.cpp Line: 64)

System.TypeInitializationException: An exception was thrown by the type initializer for Mono.CSharp.CSharpCodeCompiler ---> System.IO.FileNotFoundException: 

  at Mono.CSharp.CSharpCodeCompiler..cctor () [0x00000] in <filename unknown>:0 

  --- End of inner exception stack trace ---

  at Microsoft.CSharp.CSharpCodeProvider.CreateCompiler () [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.GenerateSerializers (System.Xml.Serialization.GenerationBatch batch, System.CodeDom.Compiler.CompilerParameters cp) [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.RunSerializerGeneration (System.Object obj) [0x00000] in <filename unknown>:0 

System.TypeInitializationException: An exception was thrown by the type initializer for Mono.CSharp.CSharpCodeCompiler ---> System.IO.FileNotFoundException: 

  at Mono.CSharp.CSharpCodeCompiler..cctor () [0x00000] in <filename unknown>:0 

  --- End of inner exception stack trace ---

  at Microsoft.CSharp.CSharpCodeProvider.CreateCompiler () [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.GenerateSerializers (System.Xml.Serialization.GenerationBatch batch, System.CodeDom.Compiler.CompilerParameters cp) [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.RunSerializerGeneration (System.Object obj) [0x00000] in <filename unknown>:0 

System.TypeInitializationException: An exception was thrown by the type initializer for Mono.CSharp.CSharpCodeCompiler ---> System.IO.FileNotFoundException: 

  at Mono.CSharp.CSharpCodeCompiler..cctor () [0x00000] in <filename unknown>:0 

  --- End of inner exception stack trace ---

  at Microsoft.CSharp.CSharpCodeProvider.CreateCompiler () [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.GenerateSerializers (System.Xml.Serialization.GenerationBatch batch, System.CodeDom.Compiler.CompilerParameters cp) [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.RunSerializerGeneration (System.Object obj) [0x00000] in <filename unknown>:0 

System.TypeInitializationException: An exception was thrown by the type initializer for Mono.CSharp.CSharpCodeCompiler ---> System.IO.FileNotFoundException: 

  at Mono.CSharp.CSharpCodeCompiler..cctor () [0x00000] in <filename unknown>:0 

  --- End of inner exception stack trace ---

  at Microsoft.CSharp.CSharpCodeProvider.CreateCompiler () [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.GenerateSerializers (System.Xml.Serialization.GenerationBatch batch, System.CodeDom.Compiler.CompilerParameters cp) [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.RunSerializerGeneration (System.Object obj) [0x00000] in <filename unknown>:0 

System.TypeInitializationException: An exception was thrown by the type initializer for Mono.CSharp.CSharpCodeCompiler ---> System.IO.FileNotFoundException: 

  at Mono.CSharp.CSharpCodeCompiler..cctor () [0x00000] in <filename unknown>:0 

  --- End of inner exception stack trace ---

  at Microsoft.CSharp.CSharpCodeProvider.CreateCompiler () [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.GenerateSerializers (System.Xml.Serialization.GenerationBatch batch, System.CodeDom.Compiler.CompilerParameters cp) [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.RunSerializerGeneration (System.Object obj) [0x00000] in <filename unknown>:0 

The referenced script on this Behaviour is missing!
 
(Filename:  Line: 1649)

Unloading 2 Unused Serialized files (Serialized files now loaded: 5)
UnloadTime: 0.797000 ms

Unloading 52 unused Assets to reduce memory usage. Loaded Objects now: 887.
Total: 41.297199 ms (FindLiveObjects: 0.030400 ms CreateObjectMapping: 0.017000 ms MarkObjects: 41.210598 ms  DeleteObjects: 0.038500 ms)

Unloading 1 Unused Serialized files (Serialized files now loaded: 5)

Unloading 2 unused Assets to reduce memory usage. Loaded Objects now: 885.
Total: 42.267700 ms (FindLiveObjects: 0.023600 ms CreateObjectMapping: 0.013800 ms MarkObjects: 42.209499 ms  DeleteObjects: 0.020200 ms)

Unloading 2 Unused Serialized files (Serialized files now loaded: 5)
UnloadTime: 0.707400 ms

Unloading 43 unused Assets to reduce memory usage. Loaded Objects now: 554.
Total: 40.763199 ms (FindLiveObjects: 0.033700 ms CreateObjectMapping: 0.014200 ms MarkObjects: 40.658897 ms  DeleteObjects: 0.055600 ms)

Unloading 0 Unused Serialized files (Serialized files now loaded: 5)

Unloading 12 unused Assets to reduce memory usage. Loaded Objects now: 556.
Total: 40.496201 ms (FindLiveObjects: 0.022500 ms CreateObjectMapping: 0.014200 ms MarkObjects: 40.432598 ms  DeleteObjects: 0.026200 ms)

The file 'D:/QuarkNetdiskDownload/HYYM/HYYM/红颜_Data/level2' is corrupted! Remove it and launch unity again!
[Position out of bounds!]
 
(Filename:  Line: 241)

Unloading 4 Unused Serialized files (Serialized files now loaded: 5)
UnloadTime: 0.591900 ms

Unloading 24 unused Assets to reduce memory usage. Loaded Objects now: 3538.
Total: 45.649101 ms (FindLiveObjects: 0.105300 ms CreateObjectMapping: 0.073600 ms MarkObjects: 45.268700 ms  DeleteObjects: 0.200700 ms)

Unloading 1 Unused Serialized files (Serialized files now loaded: 5)

Unloading 8 unused Assets to reduce memory usage. Loaded Objects now: 3710.
Total: 43.062798 ms (FindLiveObjects: 0.121900 ms CreateObjectMapping: 0.045200 ms MarkObjects: 42.867104 ms  DeleteObjects: 0.028200 ms)

Unloading 0 Unused Serialized files (Serialized files now loaded: 5)

Unloading 14 unused Assets to reduce memory usage. Loaded Objects now: 3712.
Total: 39.131298 ms (FindLiveObjects: 0.091500 ms CreateObjectMapping: 0.055700 ms MarkObjects: 38.955601 ms  DeleteObjects: 0.027900 ms)

Unloading 0 Unused Serialized files (Serialized files now loaded: 5)

Unloading 0 unused Assets to reduce memory usage. Loaded Objects now: 3714.
Total: 42.632000 ms (FindLiveObjects: 0.104600 ms CreateObjectMapping: 0.046100 ms MarkObjects: 42.476097 ms  DeleteObjects: 0.004800 ms)

Unloading 0 Unused Serialized files (Serialized files now loaded: 5)

Unloading 20 unused Assets to reduce memory usage. Loaded Objects now: 3550.
Total: 40.836201 ms (FindLiveObjects: 0.096000 ms CreateObjectMapping: 0.052300 ms MarkObjects: 40.651100 ms  DeleteObjects: 0.036000 ms)

Unloading 0 Unused Serialized files (Serialized files now loaded: 5)

Unloading 4 unused Assets to reduce memory usage. Loaded Objects now: 3632.
Total: 42.458401 ms (FindLiveObjects: 0.157200 ms CreateObjectMapping: 0.050600 ms MarkObjects: 42.224499 ms  DeleteObjects: 0.025600 ms)

Unloading 0 Unused Serialized files (Serialized files now loaded: 5)

Unloading 16 unused Assets to reduce memory usage. Loaded Objects now: 3550.
Total: 43.201202 ms (FindLiveObjects: 0.111400 ms CreateObjectMapping: 0.047300 ms MarkObjects: 43.011299 ms  DeleteObjects: 0.030600 ms)

Unloading 0 Unused Serialized files (Serialized files now loaded: 5)

Unloading 18 unused Assets to reduce memory usage. Loaded Objects now: 3552.
Total: 41.939602 ms (FindLiveObjects: 0.097500 ms CreateObjectMapping: 0.045200 ms MarkObjects: 41.764900 ms  DeleteObjects: 0.031400 ms)

