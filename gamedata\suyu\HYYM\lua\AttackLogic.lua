﻿--[[
金庸群侠传X
Mod:红颜一梦
    By:宿玉
攻击逻辑扩展
]]--

local Tools = luanet.import_type('JyGame.Tools')
local Debug = luanet.import_type('UnityEngine.Debug')
local LuaTool = luanet.import_type('JyGame.LuaTool')
local CommonSettings = luanet.import_type('JyGame.CommonSettings')
local RuntimeData = luanet.import_type('JyGame.RuntimeData')
local BattleStatus = luanet.import_type('JyGame.BattleStatus')
local  Item= luanet.import_type('JyGame.Item')
--扩展特殊攻击
function AttackLogic_extendSpecialSkill(result, skill, sourceSprite, targetSprite, bf)	
	--特技【爱的鞭笞】让队友行动
	if(skill.Name == "爱的鞭笞" and targetSprite.Team == sourceSprite.Team) then
        	targetSprite.Sp = targetSprite.Sp + 200
        	sourceSprite:AttackInfo("你不会让我受到伤害的，对吗……", Color.green)
        	bf:Log(sourceSprite.Role.Name .. "使用【爱的鞭笞】，" .. targetSprite.Role.Name .. "获得额外行动力!")
        return result
    	end

	if(skill.Name == "凯旋") then	
		if (sourceSprite.Team == 1) then
			bf.Status = BattleStatus.Win
		end
		return result
	end

	if(skill.Name == "无中生无") then
		result:AddCastInfo(sourceSprite, LuaTool.MakeStringArray({"妙手！","是我的了，呵呵……"}))
	return result
	end

	--请勿修改
	return result
end

function getskillpow(skills)
	local pow = 0
	local sk = nil
	for k,v in pairs(skills) do
		if (v.power and v.power > pow) then
			pow = v.power
			sk = v
		end
	end
	return sk
end

--扩展天赋（包含在AI计算内）
--不要在此函数中直接扣除血量、或者调用sprite的讲话函数，此函数所有的操作应该针对(AttackResult)result变量进行
function AttackLogic_extendTalents(sourceSprite, targetSprite, skill, bf, result)
	--天赋  绝对性别优势:男神  女神
	if(targetSprite.Role:HasTalent("绝世美男") and targetSprite.Team ~= sourceSprite.Team) then 
		--触发条件 有天赋 状态 战场对敌防御
		if (sourceSprite.Role.AttributesFinal["female"] == 1 and targetSprite.Role.AttributesFinal["female"] == 0) then
			--判断敌我双方都满足触发的性别条件
			--几率执行高度减伤 
			if (Tools.ProbabilityTest(0.3)) then
       			result.Hp = math.floor(result.Hp * 0.5)
				result:AddCastInfo(sourceSprite, LuaTool.MakeStringArray({"他长得好帅呀，还是打他轻一点吧", "他好帅，我都没力气了……"}), 0.75)
  			end
  		end
  	end
end

function getSixSprite(six,bf)
	local index = 0
	for _,sprite in pairs(bf.SpritesTable) do
		if(sprite.Role.AttributesFinal["female"] == six)then
			index = index + 1
		end
	end
	return index
end

--扩展天赋2（不包含在AI计算内）
--与扩展天赋区别是在此的修改是立即结算
function AttackLogic_extendTalents2(sourceSprite, targetSprite, skill, bf, result)
	
	--天赋：破碎虚空，20%概率直接将对手内力打空
	if(sourceSprite.Role:HasTalent("破碎虚空")) then
		if(Tools.ProbabilityTest(0.2)) then
			targetSprite.Mp = 0
			bf:Log(sourceSprite.Role.Name .. "天赋【破碎虚空】发动！直接打空" .. targetSprite.Role.Name .. "的内力!") --记录日志
			sourceSprite:Say("不要挣扎了，快快领死吧。")
		end
	end


--CP天赋的正确放置
if(sourceSprite.Role:HasTalent("异世人") and sourceSprite.Role.Key == "主角" and skill.Type==0) then
	local sourceSay1={"梅雪合璧","春梅绽放"}
	for _,sprite in pairs(bf.SpritesTable) do
 		if sprite.Team == sourceSprite.Team and sprite.Role:HasTalent("武学奇才") and sprite.Role.Key == "女主" then
			result.Hp = result.Hp*1.4
			--result:ClearInfo(sourceSprite)
			result:AddCastInfo(sourceSprite, sourceSay1[math.random(table.getn(sourceSay1))], 1)
			bf:Attack(sprite,skill,targetSprite.X,targetSprite.Y)
			bf:ShowSkillAnimation(skill,targetSprite.X,targetSprite.Y,callback)
			bf:Log("【"..sourceSprite.Role.Name .."】和【"..sprite.Role.Name.."】合击天赋发动！伤害增加！")
			if result.critical == false then
				if (Tools.ProbabilityTest(0.2)) then
					result.critical =true
					bf:Log(sourceSprite.Role.Name .. "合璧天赋生效！伤害暴击！")
				end
			end
			break
		end
	
	end
end


if(sourceSprite.Role:HasTalent("相见欢") and sourceSprite.Role.Key == "胡斐" and skill.Type==2) then
	local sourceSay1={"借如生死别","安得长苦悲"}
	for _,sprite in pairs(bf.SpritesTable) do
 		if sprite.Team == sourceSprite.Team and sprite.Role:HasTalent("相见欢") and sprite.Role.Key == "袁紫衣" then
			result.Hp = result.Hp*1.3
			--result:ClearInfo(sourceSprite)
			result:AddCastInfo(sourceSprite, sourceSay1[math.random(table.getn(sourceSay1))], 1)
			bf:Attack(sprite,skill,targetSprite.X,targetSprite.Y)
			bf:ShowSkillAnimation(skill,targetSprite.X,targetSprite.Y,callback)
			bf:Log("【"..sourceSprite.Role.Name .."】和【"..sprite.Role.Name.."】情侣天赋发动！伤害增加！")
			if result.critical == false then
				if (Tools.ProbabilityTest(0.3)) then
					result.critical =true
					bf:Log(sourceSprite.Role.Name .. "情侣天赋生效！伤害暴击！")
				end
			end
			break
		end
	end
end

if(sourceSprite.Role:HasTalent("心有灵犀") and sourceSprite.Role.Key == "岳灵珊" and skill.Type==1) then
	local sourceSay1={"我们一定能赢！"}
	for _,sprite in pairs(bf.SpritesTable) do
 		if sprite.Team == sourceSprite.Team and sprite.Role:HasTalent("心有灵犀") and sprite.Role.Key == "主角" then
			result.Hp = result.Hp*1.25
			--result:ClearInfo(sourceSprite)
			result:AddCastInfo(sourceSprite, sourceSay1[math.random(table.getn(sourceSay1))], 1)
			bf:Attack(sprite,skill,targetSprite.X,targetSprite.Y)
			bf:ShowSkillAnimation(skill,targetSprite.X,targetSprite.Y,callback)
			bf:Log("【"..sourceSprite.Role.Name .."】和【"..sprite.Role.Name.."】情侣天赋发动！伤害增加！")
			if result.critical == false then
				if (Tools.ProbabilityTest(0.25)) then
					result.critical =true
					bf:Log(sourceSprite.Role.Name .. "情侣天赋触发了暴击效果！")
				end
			end
			break
		end
	end
end

if(sourceSprite.Role:HasTalent("第三人格") and sourceSprite.Role.Key == "林瓶儿红衣弱" and skill.Type==1) then
	local sourceSay1={"杀人这样的事情，交给我吧！"}
	for _,sprite in pairs(bf.SpritesTable) do
 		if sprite.Team == sourceSprite.Team and sprite.Role.Key == "主角" then
			result.Hp = result.Hp*1.0
			--result:ClearInfo(sourceSprite)
			result:AddCastInfo(sourceSprite, sourceSay1[math.random(table.getn(sourceSay1))], 1)
			bf:Attack(sprite,skill,targetSprite.X,targetSprite.Y)
			bf:ShowSkillAnimation(skill,targetSprite.X,targetSprite.Y,callback)
			bf:Log("不会让任何人伤害你！")
			if result.critical == false then
				if (Tools.ProbabilityTest(0.99)) then
					result.critical =true
					bf:Log(sourceSprite.Role.Name .. "伤害暴击！")
				end
			end
			break
		end
	end
end

if(sourceSprite.Role:HasTalent("第三人格") and sourceSprite.Role.Key == "林平之" and skill.Type==1) then
	local sourceSay1={"杀人这样的事情，交给我吧！"}
	for _,sprite in pairs(bf.SpritesTable) do
		if sprite.Team == sourceSprite.Team and sprite.Role.Key == "主角" then
			result.Hp = result.Hp*1.0
			--result:ClearInfo(sourceSprite)
			result:AddCastInfo(sourceSprite, sourceSay1[math.random(table.getn(sourceSay1))], 1)
			bf:Attack(sprite,skill,targetSprite.X,targetSprite.Y)
			bf:ShowSkillAnimation(skill,targetSprite.X,targetSprite.Y,callback)
			bf:Log("不会让任何人伤害你！")
			if result.critical == false then
				if (Tools.ProbabilityTest(0.99)) then
					result.critical =true
					bf:Log(sourceSprite.Role.Name .. "伤害暴击！")
				end
			end
			break
		end
	end
end


	if (targetSprite:HasBuff("心眼") and targetSprite.Team ~= sourceSprite.Team and result.Critical) then
		local buff = targetSprite:GetBuff("心眼")
		if buff then
			result.Hp = result.Hp * (1 + 0.2 * buff.Level)
		end
	end

	if (targetSprite:HasBuff("闪避") and targetSprite.Team ~= sourceSprite.Team and Tools.ProbabilityTest(0.3)) then
		local bft = targetSprite:GetBuff("闪避")
		if bft then
			bft.Level = bft.Level + 1  -- 修改 Level
		end
		result.Hp = 0
		targetSprite:AttackInfo("闪避！",Color.green)
		bf:Log(targetSprite.Role.Name .. "闪避成功！")
	end

	if (sourceSprite:HasBuff("吸星") and targetSprite.Team ~= sourceSprite.Team) then
		local buff = sourceSprite:GetBuff("吸星")
		if buff then
			local xb = buff.Level * 0.05 * result.Hp
			if (xb > skill.CostMp * 0.5) then
				xb = skill.CostMp * 0.5
			end
			targetSprite.Mp = math.max(targetSprite.Mp - xb, 0)
			sourceSprite.Mp = math.min(sourceSprite.Mp + xb, sourceSprite.MaxMp)
			sourceSprite:AttackInfo("吸取内力" .. xb, Color.blue)
		end
	end

	if (sourceSprite:HasBuff("嗜血") and targetSprite.Team ~= sourceSprite.Team) then
		local xb = sourceSprite:GetBuff("嗜血").Level * 0.05 * result.Hp
		sourceSprite.Hp = math.min(sourceSprite.Hp + xb, sourceSprite.MaxHp)
		sourceSprite:AttackInfo("免疫伤害！", Color.green)
	end

	if (targetSprite:HasBuff("金身") and targetSprite.Team ~= sourceSprite.Team) then
		result.Hp = 0
		sourceSprite:AttackInfo("免疫伤害！"..xb,Color.green)
	end

	if (sourceSprite.Role:HasTalent("口若悬河") and targetSprite.Team ~= sourceSprite.Team and Tools.ProbabilityTest(0.2)) then
		targetSprite:AddBuff("晕眩",0,2)
		sourceSprite:AttackInfo("之乎者也……善莫大焉……",Color.green)
	end

	if (targetSprite.Role:HasTalent("泥鳅功") and targetSprite.Team ~= sourceSprite.Team and Tools.ProbabilityTest(0.7)) then
		result.Hp = 0
		bf:Log(targetSprite.Role.Name .. "身法怪异，躲避了攻击！")
	end

	if (targetSprite.Role:HasTalent("剑心通明") and targetSprite.Team ~= sourceSprite.Team) then
		result.Hp = result.Hp*0.75
	end
	if (targetSprite.Role:HasTalent("防护") and targetSprite.Team ~= sourceSprite.Team) then
		result.Hp = result.Hp*0.3
	end
	if (targetSprite.Role:HasTalent("久病成医") and targetSprite.Team ~= sourceSprite.Team and targetSprite.Hp>targetSprite.MaxHp*0.98) then
		result.Hp = 1
	end

	if (targetSprite.Role:HasTalent("太虚屏障") and targetSprite.Team ~= sourceSprite.Team) then
		if (result.Hp < targetSprite.MaxHp * 0.3) then 
			result.Hp = 0
		end
	end
	if (targetSprite.Role:HasTalent("魂伤") and targetSprite.Team ~= sourceSprite.Team) then
		result.Hp = targetSprite.MaxHp * 0.02
	end


	local munv1 = {["木婉清"] = "秦红棉", ["秦红棉"] = "木婉清"}
	if munv1[targetSprite.Role.Key] then
	for _,sprite in pairs(bf.SpritesTable) do
		if sprite.Team == targetSprite.Team and munv1[targetSprite.Role.Key] == sprite.Role.Key and Tools.ProbabilityTest(0.99) then
			result.Hp = result.Hp*0.5
			bf:Log(targetSprite.Role.Name .. "受到伤害减半！")
			break
		end
	end
	end

	local tianshanjiemei = {["霍青桐"] = "香香", ["香香"] = "霍青桐"}
	if tianshanjiemei[targetSprite.Role.Key] then
	for _,sprite in pairs(bf.SpritesTable) do
		if sprite.Team == targetSprite.Team and tianshanjiemei[targetSprite.Role.Key] == sprite.Role.Key and Tools.ProbabilityTest(0.4) then
			result.Hp = 0
			targetSprite:AttackInfo("闪避！",Color.green)
			bf:Log(targetSprite.Role.Name .. "触发天山血脉，闪避成功！")
			break
		end
	end
	end


	if(sourceSprite.Role:HasTalent("诅咒")) then
		targetSprite:AddBuff("诅咒",5,5)
	end
	if(sourceSprite.Role:HasTalent("带毒")) then
		targetSprite:AddBuff("中毒",3,3)
	end
	if (targetSprite:HasBuff("魅惑") and targetSprite.Team ~= sourceSprite.Team and targetSprite.Role.AttributesFinal["female"] ~= sourceSprite.Role.AttributesFinal["female"] and Tools.ProbabilityTest(0.5)) then
	 sourceSprite:AddBuff("晕眩",0,2)
	 bf:Log(sourceSprite.Role.Name .. "被迷晕！")
	end

	if (targetSprite:HasBuff("毒人") and targetSprite.Team ~= sourceSprite.Team) then
		if(Tools.ProbabilityTest(0.2)) then
			result.Hp = 0
 			result.Mp = 0
 			result.costBall = 0
 			result.Critical = false
 			result.Buff:Clear()
 			result.Debuff:Clear()
			local b = math.random(100,200)
			targetSprite.Sp = targetSprite.Sp + b
			targetSprite:AttackInfo("就凭你也伤的了我？",Color.green)
		end
		sourceSprite.Hp = sourceSprite.Hp*0.9
		sourceSprite:AttackInfo("你这个毒人！呃……",Color.red)
		bf:Log(sourceSprite.Role.Name .. "被毒血反噬！")
	end

	if (targetSprite:HasBuff("女诸葛") and targetSprite.Team ~= sourceSprite.Team) then
		if(Tools.ProbabilityTest(0.1)) then
			result.Hp = 0
			result.Mp = 0
			result.costBall = 0
			result.Critical = false
			result.Buff:Clear()
			result.Debuff:Clear()
			local b = math.random(100,200)
			targetSprite.Sp = targetSprite.Sp + b
			targetSprite:AttackInfo("我早猜到了！",Color.black)
		end
	end

--清DEBUFF天赋
--if targetSprite.Role:HasTalent("毒素免疫") and targetSprite:HasBuff("中毒") then
--targetSprite:DeleteBuff("中毒")
--end

	--反伤天赋
if (targetSprite.Role:HasTalent("破碎虚空")) and sourceSprite.Team~=targetSprite.Team then
  sourceSprite.Hp=sourceSprite.Hp - math.min(sourceSprite.Hp,result.Hp)
      local strmp="被〖"..targetSprite.Role.Name.."〗反伤"..tostring(math.min(sourceSprite.Hp,result.Hp))
      targetSprite:AttackInfo(strmp,Color.red)
end
if (targetSprite.Role:HasTalent("逆天煌烈")) and sourceSprite.Team~=targetSprite.Team then
    if (targetSprite:HasBuff("晕眩")) then
	sourceSprite.Hp= sourceSprite.Hp - 1
    else
	sourceSprite.Hp=sourceSprite.Hp - math.min(sourceSprite.Hp,result.Hp)
        local strmp="被〖"..targetSprite.Role.Name.."〗反伤"..tostring(math.min(sourceSprite.Hp,result.Hp))
        targetSprite:AttackInfo(strmp,Color.red)
    end
end

	--性别无敌天赋
if targetSprite.Role:HasTalent("啦啦啦啦") and sourceSprite.Team~=targetSprite.Team and sourceSprite.Role.AttributesFinal["female"]==1 then
  local rate=math.random()
  if Tools.ProbabilityTest(rate) then
    result.Hp = math.ceil(result.Hp*rate)
result:AddAttackInfo(sourceSprite, "（好帅的男人，手脚都没有力气了呢……）", Color.magenta)
  end
end

	--减伤天赋
if targetSprite.Role:HasTalent("啊啊啊啊") and sourceSprite.Team~=targetSprite.Team then
  local rate=0
  if targetSprite.Team==1 then
    rate=0.94
   elseif targetSprite.Team==2 then
    rate=0.06
  end
  result.Hp = math.min(result.Hp,math.ceil(targetSprite.MaxHp*rate))
end


--妓女天赋（已写入dll）
--if targetSprite.Role:HasTalent("淫猥下流") and sourceSprite.Team~=targetSprite.Team then
--  result.Hp=math.ceil(result.Hp*1.5)
--bf:Log(targetSprite.Role.Name .. "身体燥热难耐，受到伤害增加200%！" ) --记录日志
--end


	--几率眩晕
if sourceSprite.Role:HasTalent("黯然神伤") and sourceSprite.Team~=targetSprite.Team and Tools.ProbabilityTest(0.3) then
  if sourceSprite:AddBuffOnly2("晕眩",0,1)==true then
bf:Log("<color=green>〖"..sourceSprite.Role.Name.."〗唔……为什么……为什么……</color>")
result:AddAttackInfo(sourceSprite, "哎……为什么……会这样……", Color.green)
bf:Log(sourceSprite.Role.Name .. "在过招时想到了悲伤往事，眩晕一回合。" ) --记录日志
  end
end

if sourceSprite.Role:HasTalent("寒冰领域") and sourceSprite.Team~=targetSprite.Team then
  if Tools.ProbabilityTest(0.25) then
    if targetSprite:AddBuffOnly2("晕眩",0,1)==true then
      sourceSprite:AttackInfo("寒冰极意！",Color.blue)
bf:Log(targetSprite.Role.Name .. "在过招时被阴寒之力所伤，眩晕一回合。" ) --记录日志
    end
  end
end

	--赌徒天赋
if targetSprite.Role:HasTalent("兔起鹘落") and sourceSprite.Team~=targetSprite.Team then
  local rate=0.2
  if Tools.ProbabilityTest(0.5) then
    rate=rate+0.3
    if Tools.ProbabilityTest(0.4) then
      rate=rate+0.5
    end
  end
  result.Hp=math.ceil(result.Hp*(1-rate))
end

if targetSprite.Role:HasTalent("媚骨天成") and sourceSprite.Team~=targetSprite.Team then
  local rate=0.2
  if Tools.ProbabilityTest(0.5) then
    rate=rate+0.3
    if Tools.ProbabilityTest(0.4) then
      rate=rate+0.5
    end
  end
  result.Hp=math.ceil(result.Hp*(1-rate))
end

	--女魅魔战斗吸蓝天赋
	if (sourceSprite.Role:HasTalent("吸精魔功")) then
		if(sourceSprite.Role.AttributesFinal["female"] == 1 and targetSprite.Role.AttributesFinal["female"] == 0) then
			local mm1 = result.Hp * (Tools.GetRandomInt(3,7) * 0.2)
			local mm2 = tonumber(sourceSprite.Role.AttributesFinal["wuxing"])
			local mm3 = mm1 + 2*mm2
			local mm4 = mm2 * 0.002 + 0.55
			if sourceSprite.Role.Key ~= "主角" then
				mm4 = 0.95
			end
			if (Tools.ProbabilityTest(mm4)) then
				targetSprite.Mp = targetSprite.Mp - mm3
				sourceSprite.Mp = sourceSprite.Mp + mm3
				targetSprite:AttackInfo("被吸取精力-"..mm3, Color.black)
				sourceSprite:AttackInfo("吸取精力+"..mm3, Color.blue)
        		end
		end
	end

	if (skill.Name == "圣手回天") then
		if (sourceSprite.Team == targetSprite.Team) then
			result.Debuff:Clear()
			local addhp1 = math.min(math.floor(targetSprite.MaxHp * 0.4), tonumber(targetSprite.MaxHp - targetSprite.Hp))
			if (addhp1 > 0) then
				targetSprite.Hp = tonumber(targetSprite.Hp + addhp1)
			end
			result:AddAttackInfo(targetSprite, "+" .. tostring(addhp1), Color.green)
			bf:Log(sourceSprite.Role.Name .. "【圣手回天】发动，" .. targetSprite.Role.Name .. "恢复30%生命" ) --记录日志
		end
	elseif (skill.Name == "梅花三弄") then
		if (sourceSprite.Team == targetSprite.Team) then	
			result.Debuff:Clear()
			local addhp1 = math.min(math.floor(targetSprite.MaxHp * 0.2), tonumber(targetSprite.MaxHp - targetSprite.Hp))
			if (addhp1 > 0) then
				targetSprite.Hp = tonumber(targetSprite.Hp + addhp1)
			end
			result:AddAttackInfo(targetSprite, "+" .. tostring(addhp1), Color.green)
			bf:Log(sourceSprite.Role.Name .. "【梅花三弄】发动，" .. targetSprite.Role.Name .. "恢复15%生命" ) --记录日志
		end
	elseif (skill.Name == "百草凝魂") then
		if (sourceSprite.Team == targetSprite.Team) then
			result:AddCastInfo(sourceSprite, "复活吧，我的勇士！", 1)			
			if (RuntimeData.Instance:resurrection(0, sourceSprite.Team, 0.2, 0.1) == 0) then
				local addhp2 = math.min(math.floor(sourceSprite.MaxHp * 0.2), tonumber(sourceSprite.MaxHp - sourceSprite.Hp))
				if (addhp2 > 0) then
					sourceSprite.Hp = tonumber(sourceSprite.Hp + addhp2)
					result:AddAttackInfo(sourceSprite, "+" .. tostring(addhp2), Color.green)
				bf:Log(sourceSprite.Role.Name .. "【百草凝魂】发动，复活队友！" ) --记录日志
				end
			end
		end
	end


if(skill.Name == "缴械"  and targetSprite.Team ~= sourceSprite.Team) then
	local typ={}
	local sitem=targetSprite.Role:GetEquipment(1)
	if  sitem  then
		if (targetSprite:HasBuff("致盲") or targetSprite:HasBuff("缓速") or targetSprite:HasBuff("定身") or targetSprite:HasBuff("麻痹") or targetSprite:HasBuff("重伤") or targetSprite:HasBuff("封穴")) then
			if Tools.ProbabilityTest(0.5) then
				targetSprite.Role.Equipment:Remove(sitem)
				result:AddAttackInfo(sourceSprite, "缴械成功！", Color.magenta)
			else
				result:AddAttackInfo(sourceSprite, "缴械失败", Color.magenta)
			end
		else
			result:AddAttackInfo(sourceSprite, "需要负面状态", Color.magenta)
		end
	else
		result:AddAttackInfo(sourceSprite, "敌人没有武器", Color.magenta)		
	end
	return result
end

if(skill.Name == "破甲"  and targetSprite.Team ~= sourceSprite.Team) then
	local typ={}
	local sitem=targetSprite.Role:GetEquipment(2)
	if  sitem  then
		if (targetSprite:HasBuff("致盲") or targetSprite:HasBuff("缓速") or targetSprite:HasBuff("定身") or targetSprite:HasBuff("麻痹") or targetSprite:HasBuff("重伤") or targetSprite:HasBuff("封穴")) then
			if Tools.ProbabilityTest(0.5) then
				targetSprite.Role.Equipment:Remove(sitem)
				result:AddAttackInfo(sourceSprite, "破甲成功！", Color.magenta)
			else
				result:AddAttackInfo(sourceSprite, "破甲失败", Color.magenta)
			end
		else
			result:AddAttackInfo(sourceSprite, "需要负面状态", Color.magenta)
		end
	else
		result:AddAttackInfo(sourceSprite, "敌人没有护甲", Color.magenta)		
	end
	return result
end

if(skill.Name == "妙手"  and targetSprite.Team ~= sourceSprite.Team) then
	local toudao=0
	local typ={}
	local sourceSayFL1 = {"多谢老板！","白花花的银子！"}
	local sourceSaySI1={"神兵利器，能者得之！","你这兵器很不错！"}
	local sourceSaySI2={"我的是我的，你的还是我的！","岂曰无衣？与子同袍！"}
	local sourceSaySI3={"我的是我的，你的还是我的！","你的宝贝归我了！"}
	local sourceSaySI4={"我也来学一学这门武功！","读书读书，多多益善！"}
	if (Tools.ProbabilityTest(0.75)) then
		for i=3,4 do
			local sitem=targetSprite.Role:GetEquipment(i)
			if  sitem  then
				table.insert(typ,i)
			end
		end
		if #typ>0 then
			toudao =typ[math.random(table.getn(typ))]
			local fsitem=targetSprite.Role:GetEquipment(toudao)
			RuntimeData.Instance:GetRandomItem_extendStoryAction(fsitem,1)
			targetSprite.Role.Equipment:Remove(sitem)
		else
			toudao=1*targetSprite.Role.Level
		end
		else
			toudao=1*targetSprite.Role.Level
		end
				if (tonumber(toudao)==1) then					
					result:AddCastInfo(sourceSprite,sourceSaySI1[math.random(table.getn(sourceSaySI1))], 1)
					result:AddAttackInfo(targetSprite, "被复制武器", Color.white)
					bf:Log(sourceSprite.Role.Name .. "成功复制" .. targetSprite.Role.Name .. "的【武器】！")
				elseif (tonumber(toudao)==2) then
					result:AddCastInfo(sourceSprite,sourceSaySI2[math.random(table.getn(sourceSaySI2))], 1)
					result:AddAttackInfo(targetSprite, "被复制防具", Color.white)
					bf:Log(sourceSprite.Role.Name .. "成功复制" .. targetSprite.Role.Name .. "的【防具】！")
				elseif (tonumber(toudao)==3) then
					result:AddCastInfo(sourceSprite,sourceSaySI3[math.random(table.getn(sourceSaySI3))], 1)
					bf:Log(sourceSprite.Role.Name .. "成功偷取" .. targetSprite.Role.Name .. "的饰品！")
				elseif (tonumber(toudao)==4) then
					result:AddCastInfo(sourceSprite,sourceSaySI4[math.random(table.getn(sourceSaySI4))], 1)
					bf:Log(sourceSprite.Role.Name .. "成功偷取" .. targetSprite.Role.Name .. "的随身物品！")
				else
					RuntimeData.Instance.Money = RuntimeData.Instance.Money + tonumber(toudao)
					result:AddCastInfo(sourceSprite, sourceSayFL1[math.random(table.getn(sourceSayFL1))], 1)
					result:AddAttackInfo(targetSprite, "偷取$" .. tostring(toudao), Color.white)
					local mint = math.abs(sourceSprite.Role.AttributesFinal["shenfa"]+sourceSprite.Role.AttributesFinal["bili"])
					targetSprite.Hp =math.max(targetSprite.Hp- math.min(100 + mint,targetSprite.MaxHp*0.3),1)
					bf:Log(sourceSprite.Role.Name .. "成功从" .. targetSprite.Role.Name .. "处偷取【" .. tostring(toudao) .. "】两，并顺手打了他一下！")
				end
				
		end

		return result	
	end




--扩展天赋3（包含在AI计算内，并参与计算公式）
--与扩展天赋区别是参与了最小攻击、最大攻击、暴击、防御相关的计算公式，但请不要对result.Hp进行赋值
function AttackLogic_extendTalents3(sourceSprite, targetSprite, skill, bf, result, formula)
	--天赋：女性（特定武功对男性的攻击加倍）
	--if(sourceSprite.Role:HasTalent("女性") and targetSprite.Role.AttributesFinal["female"] == 0) then 
		--if (skill.Name == "男人见不得" or skill.Name == "绝户虎爪手" or skill.Name == "弹指神通.tjjtds") then
			--formula.attackLow = formula.attackLow * 2
			--if (formula.attackLow < 0) then
			--	formula.attackLow = 0
			--end
			--formula.attackUp = formula.attackUp * 2
		--end
	--end
	--天赋：（特定武功对女性的攻击加倍）
	--if (targetSprite.Role.AttributesFinal["female"] == 1) then 
		--if ( skill.Name == "勾魂指") then
			--formula.attackLow = formula.attackLow * 2
			--if (formula.attackLow < 0) then
				--formula.attackLow = 0
			--end
			--formula.attackUp = formula.attackUp * 2
		--end
	--end
	--天赋：小家碧玉（最小攻击降低30%，最大攻击提高200%）
	--if(sourceSprite.Role:HasTalent("999999") and skill.Name == "花拳绣腿") then
		--formula.attackLow = formula.attackLow * 0.7
        	--if (formula.attackLow < 0) then
            		--formula.attackLow = 0
       		--end
 		--formula.attackUp = formula.attackUp * 3.0
	--end
	--自定义攻防类
	--if(sourceSprite.Role:HasTalent("天下无双")) then
		--formula.criticalHit = math.max(formula.criticalHit, 2.0)
		--formula.attackLow = math.abs(formula.attackLow * 1.3)
		--formula.attackUp = math.abs(formula.attackUp * 1.8)
	--end
end
